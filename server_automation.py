import logging
import subprocess
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON>t
import vdf
from apscheduler.schedulers.background import BackgroundScheduler
from flask import Flask, request, jsonify, send_from_directory

# Configure logging
logging.basicConfig(format='%(asctime)s - %(levelname)s: %(message)s', level=logging.INFO)

# Define the list of configurations
configs = [
    {"username": "euof6297", "password": "213844851", "app_ids": ["1938090"]},
    # Add more configurations as needed
    {"username": "another_user", "password": "another_password", "app_ids": ["123456", "654321"]}
]

PASSWORD = "EZPZ"

def load_key():
    return open("secret.key", "rb").read()

def decrypt_message(encrypted_message):
    key = load_key()
    f = Fernet(key)
    decrypted_message = f.decrypt(encrypted_message.encode()).decode()
    return decrypted_message

def authenticate_request(request):
    encrypted_password = request.args.get('password') or (request.json and request.json.get('password'))
    if not encrypted_password:
        return False
    decrypted_password = decrypt_message(encrypted_password)
    return decrypted_password == PASSWORD
def run_script(username, password, app_ids, remove_old):
    app_ids_str = ','.join(app_ids)
    remove_old_flag = '-r' if remove_old else ''
    command = f'python3 manifest_generator.py -u {username} -p {password} -a {app_ids_str} {remove_old_flag}'

    logging.info(f'Executing: {command}')

    try:
        result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logging.info(f'Success: {result.stdout.decode()}')
    except subprocess.CalledProcessError as e:
        logging.error(f'Error: {e.stderr.decode()}')

def schedule_jobs(scheduler):
    for config in configs:
        scheduler.add_job(run_script, 'interval', minutes=30, args=[config['username'], config['password'], config['app_ids'], True])

def start_scheduler():
    # Run the script immediately for each configuration
    for config in configs:
        run_script(config['username'], config['password'], config['app_ids'], True)

    # Schedule the jobs
    scheduler = BackgroundScheduler()
    schedule_jobs(scheduler)

    try:
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        pass

# Flask app setup
app = Flask(__name__)

DEPOTS_DIR = './depots'

@app.route('/get_manifest', methods=['GET'])
def get_manifest():
    app_id = request.args.get('app_id')
    app_dir = Path(DEPOTS_DIR) / app_id

    if not app_dir.exists():
        return jsonify({'error': 'App ID not found'}), 404

    manifests = [f.name for f in app_dir.glob('*.manifest')]
    config_path = app_dir / 'config.vdf'

    if not config_path.exists():
        return jsonify({'error': 'config.vdf not found'}), 404

    with open(config_path) as f:
        config_data = vdf.load(f)

    return jsonify({'manifests': manifests, 'config': config_data})

@app.route('/download_manifest/<app_id>/<filename>', methods=['GET'])
def download_manifest(app_id, filename):

    if not authenticate_request(request):
        return jsonify({'error': 'Unauthorized'}), 401

    app_dir = Path(DEPOTS_DIR) / app_id

    if not app_dir.exists() or not (app_dir / filename).exists():
        return jsonify({'error': 'File not found'}), 404

    return send_from_directory(app_dir, filename, as_attachment=True)

@app.route('/update_client_config', methods=['POST'])
def update_client_config():

    if not authenticate_request(request):
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    app_id = data['app_id']

    # 获取对应的服务器端 config.vdf 文件路径
    server_config_path = Path(DEPOTS_DIR) / app_id / 'config.vdf'

    if not server_config_path.exists():
        return jsonify({'error': 'Server config.vdf not found'}), 404

    # 读取服务器端 config.vdf
    with open(server_config_path, 'r', encoding='utf-8') as f:
        server_config_data = vdf.load(f)

    # 只返回 depots 部分给客户端
    return jsonify(server_config_data['depots'])

if __name__ == '__main__':
    from threading import Thread

    # Start the scheduler in a separate thread
    scheduler_thread = Thread(target=start_scheduler)
    scheduler_thread.start()

    # Run the Flask app
    app.run(host='0.0.0.0', port=5000)
