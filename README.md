# Steam Manifest Updater

A comprehensive Steam game management system that provides license-based access to Steam game manifests with both CLI and GUI interfaces.

## Project Structure

```
📁 SteamManifestUpdater/
├── 📁 src/                    # Source code files
│   ├── client_tool.py         # Main client application (CLI)
│   ├── client_tool_gui.py     # GUI version of client tool
│   ├── mini_client_tool.py    # Lightweight monitoring client
│   ├── manifest_generator.py  # Steam manifest downloader
│   ├── server_automation.py   # Server-side automation tool
│   ├── keyauth.py            # KeyAuth API client library
│   ├── encryption_tool.py    # Encryption utilities
│   ├── tools.py              # System utility functions
│   └── test.py               # Steam activation window detector
├── 📁 config/                 # Configuration files
│   ├── requirements.txt      # Python dependencies
│   ├── MTYB_SteamTool.spec  # PyInstaller spec for main tool
│   ├── MTYB_MiniSteamTool.spec # PyInstaller spec for mini tool
│   ├── MTYB_Test.spec       # PyInstaller spec for test tool
│   └── TEST.spec            # Alternative test spec
├── 📁 scripts/               # PowerShell and batch scripts
│   ├── download_script.ps1   # Download automation script
│   ├── Steam_Developer_API.ps1 # Steam API utilities
│   └── Packing.cmd          # Build packaging script
├── 📁 assets/                # Images and resources
│   ├── icon.ico             # Application icon
│   └── steam_activate_product.png # Template for OCR detection
├── 📁 cache/                 # Cache and temporary files
│   ├── __pycache__/         # Python bytecode cache
│   └── app.log              # Application log file
├── 📁 depots/                # Steam game depot data
│   └── [various game IDs]/   # Individual game depot folders
├── secret.key               # Encryption key (keep secure)
└── README.md                # This file
```

## Key Features

- **License-based Authentication**: Uses KeyAuth for secure access control
- **Steam Integration**: Downloads and manages Steam game manifests
- **Multiple Interfaces**: Both command-line and GUI versions available
- **Server Automation**: Flask-based server for automated manifest generation
- **Encryption**: Secure data storage and transmission
- **System Integration**: Windows firewall and antivirus management
- **OCR Automation**: Automatic Steam key activation detection

## Usage

### Running the Main Client
```bash
cd src
python client_tool.py --auth
```

### Running the GUI Version
```bash
cd src
python client_tool_gui.py
```

### Running the Mini Client (Background Monitoring)
```bash
cd src
python mini_client_tool.py
```

### Building Executables
```bash
# From project root
pyinstaller config/MTYB_SteamTool.spec
pyinstaller config/MTYB_MiniSteamTool.spec
```

## Installation

1. Install Python dependencies:
```bash
pip install -r config/requirements.txt
```

2. Ensure you have a valid license key for authentication

3. Run the desired tool from the `src/` directory

## Important Files

- `secret.key`: Encryption key used by the system (keep secure)
- `src/keyauth.py`: Handles all authentication logic
- `src/manifest_generator.py`: Core Steam manifest downloading functionality
- `config/requirements.txt`: All Python dependencies

## Notes

- All Python source files are now organized in the `src/` folder
- Configuration files are in the `config/` folder
- Build artifacts will be generated in `build/` and `dist/` folders when building
- The project maintains backward compatibility with existing functionality
