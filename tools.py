import platform
import subprocess


def convert_to_windows_path(file_path):
    return file_path.replace("/", "\\")


def check_firewall_rule_exists(rule_name):
    command = f"netsh advfirewall firewall show rule name={rule_name}"
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0 and rule_name in result.stdout:
            return True
        else:
            return False
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        return False


def add_firewall_rule(program_path, ruleName):
    rule_name = ruleName

    if check_firewall_rule_exists(rule_name):
        print(f"Firewall rule '{rule_name}' already exists. Skipping addition.")
        return

    command = f"netsh advfirewall firewall add rule name={rule_name} dir=out action=block program=\"{program_path}\""
    try:
        subprocess.run(command, shell=True, check=True)
        print(f"Firewall rule added to block outbound for {program_path}")
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        print("Failed to add firewall rule.")


def add_defender_exception(exclusion_path):
    try:
        # Use PowerShell command to add the exclusion path
        powershell_cmd = f"Add-MpPreference -ExclusionPath '{exclusion_path}'"
        subprocess.run(["powershell", "-Command", powershell_cmd], check=True)
        print(f"Exclusion added for: {exclusion_path}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to add exclusion: {exclusion_path}. Error: {e}")


def add_host_entry(hostname, ip_address):
    system = platform.system()
    if system == 'Windows':
        hosts_path = r'C:\Windows\System32\drivers\etc\hosts'
    elif system == 'Linux':
        hosts_path = '/etc/hosts'
    else:
        raise NotImplementedError(f'Unsupported operating system: {system}')

    with open(hosts_path, 'r') as f:
        hosts_content = f.read()

    entry = f'{ip_address} {hostname}\n'
    if entry in hosts_content:
        print(f"Entry already exists, Skipping...")
        return

    with open(hosts_path, 'a') as f:
        f.write(entry)