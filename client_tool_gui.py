import hashlib
import os
import platform
import sys
import json
from webbrowser import get

import requests
import win32security
import requests  # https requests
from Crypto.Cipher import AES
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad
import winreg
from pathlib import Path
import argparse
import requests
import vdf
import sqlite3
from datetime import datetime
from keyauth import api
from cryptography.fernet import Fernet
import subprocess
import time
import webbrowser

class colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'

def getSteamPath():
    reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Valve\\Steam")
    steam_path = winreg.QueryValueEx(reg_key, "SteamPath")[0]
    winreg.CloseKey(reg_key)

    if steam_path is None:
        print("[MTYB] Steam is not installed.")
        time.sleep(5)
        exit(0)

    return steam_path


# Define the server URL
key = ""
STEAM_PATH = getSteamPath()
SERVER_URL = "https://manifest.online-mtyb.com"
DATABASE_FILE = os.path.join(STEAM_PATH, 'app.log')
KEY_FILE = os.path.join(STEAM_PATH, 'app.info.log')
LOCK_FILE = os.path.join(STEAM_PATH, 'monitor.lock')

def generate_key():
    key = Fernet.generate_key()
    with open(KEY_FILE, 'wb') as key_file:
        key_file.write(key)

# Load the key from the file
def load_key():
    return open(KEY_FILE, 'rb').read()


# Encrypt a message
def encrypt_message(message, key):
    f = Fernet(key)
    return f.encrypt(message.encode())


# Decrypt a message
def decrypt_message(encrypted_message, key):
    f = Fernet(key)
    return f.decrypt(encrypted_message).decode()


# Generate and load key (only needed once)
if not os.path.exists(KEY_FILE):
    generate_key()
KEY = load_key()


def clear():
    if platform.system() == 'Windows':
        os.system('cls & title MTYB Official Tool')  # clear console, change title
    elif platform.system() == 'Linux':
        os.system('clear')  # clear console
        sys.stdout.write("\x1b]0;MTYB Official Tool\x07")  # change title
    elif platform.system() == 'Darwin':
        os.system("clear && printf '\e[3J'")  # clear console
        os.system('''echo - n - e "\033]0;MTYB Official Tool\007"''')  # change title


def getchecksum():
    md5_hash = hashlib.md5()
    file = open(''.join(sys.argv[0]), "rb")
    md5_hash.update(file.read())
    digest = md5_hash.hexdigest()
    return digest


keyauthapp = api(
    name="MainSteam",
    ownerid="1tGVnUKtzH",
    secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
    version="1.0",
    hash_to_check=getchecksum()
)

SERVER_URL = keyauthapp.var("SERVER_API")

def ensure_dir_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


def get_manifest(app_id):
    response = requests.get(f"{SERVER_URL}/get_manifest?app_id={app_id}")
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to retrieve data for {app_id}")
        return None

def clearCache():
    steam_path = getSteamPath()
    appinfo_path = os.path.join(steam_path, 'appcache', 'appinfo.vdf')
    packageinfo_path = os.path.join(steam_path, 'appcache', 'packageinfo.vdf')

    # 检查文件是否存在并删除它们
    if os.path.exists(appinfo_path):
        os.remove(appinfo_path)

    if os.path.exists(packageinfo_path):
        os.remove(packageinfo_path)

def remove_lock():
    if os.path.exists(LOCK_FILE):
        os.remove(LOCK_FILE)

def update_client_config(app_id, client_base_dir):
    data = {
        'app_id': app_id,
        'password': "gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw=="
    }

    response = requests.post(f"{SERVER_URL}/update_client_config", json=data)

    if response.status_code == 200:
        print(f"{colors.GREEN}[MTYB] Config Updated Successfully ✅{colors.RESET}")
    else:
        print(f"{colors.RED}[MTYB] Failed To Update Client Config ❌{colors.RESET}")
        return

    client_config_path = Path(client_base_dir) / 'config' / 'config.vdf'

    if not client_config_path.exists():
        print(f"{colors.RED}[MTYB] Client Config Not Found ❌{colors.RESET}")
        return

    server_depots = response.json()

    # Load client config
    with open(client_config_path, 'r', encoding='utf-8') as f:
        client_config = vdf.load(f)

    # Ensure the necessary structure exists in client config
    depots_path = ["InstallConfigStore", "Software", "Valve", "Steam", "depots"]
    current = client_config
    for key in depots_path:
        if key not in current:
            current[key] = {}
        current = current[key]

    # Update the client config depots with server depots
    current.update(server_depots)

    # Write the updated client config back to file
    with open(client_config_path, 'w', encoding='utf-8') as f:
        vdf.dump(client_config, f, pretty=True)


def download_manifest(app_id, manifest_file, download_path):
    response = requests.get(f"{SERVER_URL}/download_manifest/{app_id}/{manifest_file}?password=gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw==")
    if response.status_code == 200:
        with open(download_path, 'wb') as f:
            f.write(response.content)
    else:
        print(f"{colors.RED}[MTYB] Failed To Download File ❌{colors.RESET}\n")


def get_connection():
    return sqlite3.connect(DATABASE_FILE)


def create_license(license_key, app_id):
    conn = get_connection()
    cursor = conn.cursor()
    created_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    encrypted_license_key = encrypt_message(license_key, KEY)
    encrypted_app_id = encrypt_message(app_id, KEY)

    cursor.execute('''
        INSERT INTO licenses (license_key, created_date, app_id)
        VALUES (?, ?, ?)
    ''', (encrypted_license_key, created_date, encrypted_app_id))

    conn.commit()

    # Fetch the app info and its DLCs
    data = keyauthapp.var("Main")
    app_info = {}

    if data is not None:
        json_data = json.loads(data)
        for app in json_data["apps"]:
            if license_key.startswith(app["license_prefix"]):
                app_info = app

    if app_info:
        dlcs = keyauthapp.var(app_info["app_name"])
        if dlcs is not None:
            dlcs_data = json.loads(dlcs)
            for dlc_id in dlcs_data["dlcs"]:
                cursor.execute('''
                        INSERT INTO dlcs (license_key, dlc_id)
                        VALUES (?, ?)
                    ''', (encrypted_license_key, encrypt_message(str(dlc_id), KEY)))

    conn.commit()

    conn.close()
    print(f"{colors.GREEN}[MTYB] License recognized successfully ✅{colors.RESET}\n")


def license_exists(license_key):
    conn = get_connection()
    cursor = conn.cursor()
    cursor.execute('''
            SELECT * FROM licenses
        ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            if(decrypted_license_key == license_key):
                return True

    return False

def get_first_license():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
            SELECT * FROM licenses 
        ''')

    row = cursor.fetchone()
    conn.close()

    if row:
        decrypted_license_key = decrypt_message(row[0], KEY)
        decrypted_app_id = decrypt_message(row[2], KEY)
        return {'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id}
    else:
        print("First License Not Found.")
        return None

def read_all_license():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT * FROM licenses
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        licenses = []
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            decrypted_app_id = decrypt_message(row[2], KEY)
            licenses.append({'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id})
        return licenses
    else:
        print("License not found.")
        return None

def read_license(license_key):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)

    cursor.execute('''
        SELECT * FROM licenses
        ORDER BY created_date DESC
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            decrypted_app_id = decrypt_message(row[2], KEY)
            return {'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id}
    else:
        print("License not found.")
        return None


def read_all_app_id():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT license_key, created_date, app_id FROM licenses
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        app_ids = []
        for row in rows:
            decrypted_app_id = decrypt_message(row[2], KEY)
            app_ids.append(decrypted_app_id)
        return app_ids
    else:
        print("[MTYB] No Licenses.")
        return None


def update_license(license_key, new_app_id):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)
    encrypted_new_app_id = encrypt_message(new_app_id, KEY)

    cursor.execute('''
        UPDATE licenses SET app_id = ? WHERE license_key = ?
    ''', (encrypted_new_app_id, encrypted_license_key))

    conn.commit()
    conn.close()
    print("License updated successfully.")


def delete_license(license_key):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)

    cursor.execute('''
        DELETE FROM licenses WHERE license_key = ?
    ''', (encrypted_license_key,))

    conn.commit()
    conn.close()
    print("License deleted successfully.")


def main(app_id):
    try:
        steam_path = STEAM_PATH

        if steam_path is None:
            print("Steam is not installed.")
            return

        client_base_path = Path(steam_path)

        # Ensure client base path exists
        ensure_dir_exists(client_base_path)

        # Retrieve manifest files and config.vdf content from server
        manifest_data = get_manifest(app_id)
        if manifest_data is None:
            return

        # Ensure client's depotcache directory exists
        client_depotcache_path = client_base_path / 'depotcache'
        ensure_dir_exists(client_depotcache_path)

        # Append server config.vdf content to client's config.vdf
        update_client_config(app_id, client_base_path)

        # Download manifest files from server to client's depotcache directory
        for manifest_file in manifest_data['manifests']:
            download_path = client_depotcache_path / manifest_file
            download_manifest(app_id, manifest_file, download_path)

    except Exception as e:
        print(f"Error: {str(e)}")


def update(app_id):
    try:
        steam_path = STEAM_PATH

        if steam_path is None:
            print("Steam is not installed.")
            return

        client_base_path = Path(steam_path)

        # Ensure client base path exists
        ensure_dir_exists(client_base_path)

        # Retrieve manifest files and config.vdf content from server
        manifest_data = get_manifest(app_id)
        if manifest_data is None:
            return

        # Ensure client's depotcache directory exists
        client_depotcache_path = client_base_path / 'depotcache'
        ensure_dir_exists(client_depotcache_path)

        # Append server config.vdf content to client's config.vdf
        update_client_config(app_id, client_base_path)

        # Download manifest files from server to client's depotcache directory
        for manifest_file in manifest_data['manifests']:
            download_path = client_depotcache_path / manifest_file
            download_manifest(app_id, manifest_file, download_path)

    except Exception as e:
        print(f"Error: {str(e)}")


def authenticate():
    global key
    key = input(f'{colors.WHITE} Enter License Key : {colors.RESET}')
    isLoggedIn = keyauthapp.license(key)
    authenticated = False
    if isLoggedIn:
        print(f"{colors.BLUE}[MTYB] Checking Relavant Infomation... ⏳{colors.RESET}\n")
        authenticated = True

        data = keyauthapp.var("Main")
        app_info = {}

        if (data != None):
            json_data = json.loads(data)
            for app in json_data["apps"]:
                if key.startswith(app["license_prefix"]):
                    app_info = app
        else:
            print("Data Is Not Found, Please Contact Seller.")

        if app_info != {}:
            if (license_exists(key) == False and app_info.get('app_id') != None):
                create_license(key, app_info.get('app_id'))

        else:
            print("App Not Found.")

    return authenticated


def checkDatabase():
    if not os.path.exists(DATABASE_FILE):
        print(f"{DATABASE_FILE} does not exist. Creating a new database.")

        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                license_key BLOB PRIMARY KEY,
                created_date TEXT,
                app_id BLOB
            )
        ''')

        conn.commit()

        cursor.execute('''
                CREATE TABLE IF NOT EXISTS dlcs (
                    license_key TEXT,
                    dlc_id TEXT,
                    FOREIGN KEY(license_key) REFERENCES licenses(license_key)
                )
            ''')

        conn.commit()
        conn.close()

        print("Database created successfully with an empty table.")

def convert_to_windows_path(file_path):
    return file_path.replace("/", "\\")

def add_defender_exception(exclusion_path):
    try:
        # Use PowerShell command to add the exclusion path
        powershell_cmd = f"Add-MpPreference -ExclusionPath '{exclusion_path}'"
        subprocess.run(["powershell", "-Command", powershell_cmd], check=True)
        print(f"Windows Defender exclusion added for {exclusion_path}.")
    except subprocess.CalledProcessError as e:
        clear()

def checkMiniTool():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = "steamclient32.dll"
    target_file = steam_path / filename

    reset = keyauthapp.var("Reset")
    run = True

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if run:
        url = keyauthapp.var("MiniTool_steamclient32.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")


def checkGreenLuma():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = "User32.dll"
    target_file = steam_path / filename
    reset = keyauthapp.var("Reset")
    run = False

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if run:
        url = keyauthapp.var("GreenLuma_User32.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

    licenses = read_all_license()
    main_info = json.loads(keyauthapp.var("Main"))
    app_names = []
    dlcs_list = []
    if len(licenses) > 0:
        for license in licenses:
            if keyauthapp.license(license['license_key']) != False:
                for app in main_info["apps"]:
                    if app["app_id"] == license["app_id"]:
                        app_names.append(app["app_name"])

        # 获取DLCs列表
        for app_name in app_names:
            data = keyauthapp.var(app_name)
            if data is not None:
                data = json.loads(keyauthapp.var(app_name))
                dlcs_list.extend(data["dlcs"])

    # 创建存储DLCs ID的目录
    steam_path = Path(getSteamPath()) / "AppList"
    os.makedirs(steam_path, exist_ok=True)

    # 将DLCs ID写入文件
    for i, dlc_id in enumerate(dlcs_list):
        with open(os.path.join(steam_path, f"{i}.txt"), 'w') as file:
            file.write(str(dlc_id))


def addDefenderExclusion():
    try:
        exclusion_path = convert_to_windows_path(STEAM_PATH) + r"\*"
        add_defender_exception(exclusion_path)
    except Exception as e:
        print("[MTYB] Please disable your anti-virus. It blocked the tool.")
        time.sleep(5)
        exit(0)


def checkKoaLoader():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = 'hid.dll'
    unlocker_name = 'Lyptus.dll'
    configname = "Koaloader.config.json"
    target_file = steam_path / filename
    target_unlocker = steam_path / unlocker_name
    target_config = steam_path / configname
    reset = keyauthapp.var("Reset")
    run = False

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if not os.path.exists(target_config):
        run = True

    if run:
        url = keyauthapp.var("KoaLoader_hid.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

        url = keyauthapp.var("KoaLoader_config")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_config, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

        url = keyauthapp.var("Starter_Lyptus.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_unlocker, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")


if __name__ == '__main__':
    print(f"[MTYB] Tools Initializing...\n")
    addDefenderExclusion()
    checkDatabase()

    clear()

    parser = argparse.ArgumentParser(description='Process some parameters.')
    parser.add_argument('--auth', action='store_true', help='Run authentication process')

    args = parser.parse_args()

    if args.auth:
        authenticated = False
        while not authenticated:
            authenticated = authenticate()

            if authenticated:
                remove_lock()
                clearCache()
                checkKoaLoader()
                checkGreenLuma()
                checkMiniTool()
                lic = read_license(key)
                if(lic == None):
                    print("Error License Key Not Added Yet.")
                else:
                    main(lic['app_id'])
                    webbrowser.open("steam://install/" + lic['app_id'])
    else:
        app_ids = read_all_app_id()
        if (app_ids != None):
            keyauthapp.license(get_first_license()['license_key'])
            remove_lock()
            clearCache()
            checkGreenLuma()
            checkKoaLoader()

            for app_id in app_ids:
                update(app_id)
        else:
            authenticated = False
            while not authenticated:
                authenticated = authenticate()
                if authenticated:
                    remove_lock()
                    clearCache()
                    checkKoaLoader()
                    checkGreenLuma()
                    checkMiniTool()

                    lic = read_license(key)
                    if (lic == None):
                        print("Error License Key Not Added Yet.")
                    else:
                        main(lic['app_id'])
