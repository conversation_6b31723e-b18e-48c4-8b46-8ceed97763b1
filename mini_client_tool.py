import os
import sqlite3
import json
import vdf
import psutil
import threading
import requests
import winreg
from pathlib import Path
from cryptography.fernet import Fernet
import time

def get_steam_path():
    try:
        reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Valve\\Steam")
        steam_path = winreg.QueryValueEx(reg_key, "SteamPath")[0]
        winreg.CloseKey(reg_key)
        return steam_path
    except:
        return None


STEAM_PATH = get_steam_path()
KEY_FILE = os.path.join(STEAM_PATH, 'app.info.log')
DATABASE_FILE = os.path.join(STEAM_PATH, 'app.log')
SERVER_URL = "https://manifest.online-mtyb.com"
LOCK_FILE = os.path.join(STEAM_PATH, 'monitor.lock')
monitor_threads = []  # 用于存储后台线程

def load_key():
    return open(KEY_FILE, 'rb').read()

def decrypt_message(encrypted_message, key):
    f = Fernet(key)
    return f.decrypt(encrypted_message).decode()

def get_connection():
    return sqlite3.connect(DATABASE_FILE)

def read_all_app_id():
    conn = get_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT app_id FROM licenses')
    rows = cursor.fetchall()
    conn.close()
    if rows:
        return [decrypt_message(row[0], load_key()) for row in rows]
    return []

def get_manifest(app_id):
    response = requests.get(f"{SERVER_URL}/get_manifest?app_id={app_id}")
    return response.json() if response.status_code == 200 else None

def download_manifest(app_id, manifest_file, download_path):
    response = requests.get(f"{SERVER_URL}/download_manifest/{app_id}/{manifest_file}?password=gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw==")
    if response.status_code == 200:
        with open(download_path, 'wb') as f:
            f.write(response.content)
    elif response.status_code == 401:
        return

def update_client_config(app_id, client_base_dir):
    data = {
        'app_id': app_id,
        'password': "gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw=="
    }
    response = requests.post(f"{SERVER_URL}/update_client_config", json=data)

    if response.status_code == 200:
        client_config_path = Path(client_base_dir) / 'config' / 'config.vdf'

        if not client_config_path.exists():
            return

        server_depots = response.json()

        # Load client config
        with open(client_config_path, 'r', encoding='utf-8') as f:
            client_config = vdf.load(f)

        # Ensure the necessary structure exists in client config
        depots_path = ["InstallConfigStore", "Software", "Valve", "Steam", "depots"]
        current = client_config
        for key in depots_path:
            if key not in current:
                current[key] = {}
            current = current[key]

        # Update the client config depots with server depots
        current.update(server_depots)

        # Write the updated client config back to file
        with open(client_config_path, 'w', encoding='utf-8') as f:
            vdf.dump(client_config, f, pretty=True)
    elif response.status_code == 401:
        return



def clearCache():
    steam_path = STEAM_PATH
    appinfo_path = os.path.join(steam_path, 'appcache', 'appinfo.vdf')
    packageinfo_path = os.path.join(steam_path, 'appcache', 'packageinfo.vdf')

    # 检查文件是否存在并删除它们
    if os.path.exists(appinfo_path):
        os.remove(appinfo_path)

    if os.path.exists(packageinfo_path):
        os.remove(packageinfo_path)

def update(app_id):
    try:
        client_base_path = Path(STEAM_PATH)
        client_depotcache_path = client_base_path / 'depotcache'
        os.makedirs(client_depotcache_path, exist_ok=True)
        manifest_data = get_manifest(app_id)
        if manifest_data:
            for manifest_file in manifest_data['manifests']:
                download_path = client_depotcache_path / manifest_file
                download_manifest(app_id, manifest_file, download_path)
            update_client_config(app_id, client_base_path)
    except Exception as e:
        pass
def fetch_all_dlcs():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT dlc_id FROM dlcs
    ''')

    rows = cursor.fetchall()
    conn.close()

    dlcs_list = [decrypt_message(row[0], load_key()) for row in rows]

    steam_path = Path(STEAM_PATH) / "AppList"
    os.makedirs(steam_path, exist_ok=True)

    # 将DLCs ID写入文件
    for i, dlc_id in enumerate(dlcs_list):
        with open(os.path.join(steam_path, f"{i}.txt"), 'w') as file:
            file.write(str(dlc_id))

def check_and_create_lock():
    if os.path.exists(LOCK_FILE):
        return False
    else:
        with open(LOCK_FILE, 'w') as f:
            f.write(str(os.getpid()))
        return True

def remove_lock():
    if os.path.exists(LOCK_FILE):
        os.remove(LOCK_FILE)

def monitor_and_update_background(app_id):
    def monitor_and_update():
        client_config_path = Path(STEAM_PATH) / 'config' / 'config.vdf'
        last_mod_time = None

        while True:
            if not any(proc.name() == "steam.exe" for proc in psutil.process_iter()):  # 检查 steam.exe 是否正在运行
                remove_lock()  # 移除锁文件
                break  # 退出线程

            try:
                # 获取 config 文件的当前修改时间
                current_mod_time = os.path.getmtime(client_config_path)

                # 如果文件被修改，更新客户端配置
                if last_mod_time is None or current_mod_time != last_mod_time:
                    manifest_data = get_manifest(app_id)
                    if manifest_data:
                        server_depots = manifest_data['manifests']
                        update_client_config(app_id, STEAM_PATH)
                        last_mod_time = current_mod_time

                # 等待一段时间后再次检查
                time.sleep(10)
            except Exception as e:
                print(f"Error: {e}")
                time.sleep(10)

    monitor_thread = threading.Thread(target=monitor_and_update, daemon=True)
    monitor_threads.append(monitor_thread)  # 添加线程到全局列表
    monitor_thread.start()

if __name__ == '__main__':
    if STEAM_PATH:
        if check_and_create_lock():
            try:
                app_ids = read_all_app_id()
                for app_id in app_ids:
                    clearCache()
                    update(app_id)
                    fetch_all_dlcs()
                    monitor_and_update_background(app_id)  # 启动监控和更新任务

                # 等待所有监控线程完成
                for thread in monitor_threads:
                    thread.join()

            finally:
                remove_lock()
        else:
            app_ids = read_all_app_id()
            for app_id in app_ids:
                clearCache()
                update(app_id)
                fetch_all_dlcs()
