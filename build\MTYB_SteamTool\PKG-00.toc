('D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\MTYB_SteamTool.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('client_tool',
   'D:\\SourceCode\\SteamManifestUpdater\\client_tool.py',
   'PYSOURCE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\base_library.zip',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\METADATA',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\REQUESTED',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\top_level.txt',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('wheel-0.38.4.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.38.4.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.38.4.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\METADATA',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\entry_points.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\LICENSE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.38.4.dist-info\\LICENSE.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\entry_points.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\entry_points.txt',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
