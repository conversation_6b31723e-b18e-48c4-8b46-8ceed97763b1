import pyautogui
import cv2
import numpy as np
import pytesseract
import time

# 配置Tesseract的路径（如果需要）
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# 模板图像路径
template_path = 'steam_activate_product.png'

# 加载模板图像
template = cv2.imread(template_path, 0)
w, h = template.shape[::-1]


def match_template():
    # 截取当前屏幕
    screenshot = pyautogui.screenshot()
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_BGR2GRAY)

    # 使用OpenCV进行模板匹配
    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
    threshold = 0.8  # 匹配阈值，可以调整
    loc = np.where(result >= threshold)

    # 如果找到匹配，提取文本框内容
    if len(loc[0]) > 0:
        print("Detected Steam activation window!")
        for pt in zip(*loc[::-1]):
            extract_text((pt[0], pt[1], pt[0] + w, pt[1] + h))
            break


def extract_text(region):
    x1, y1, x2, y2 = region
    # 假设文本框位于匹配区域的固定偏移位置
    textbox_region = (x1 + 20, y1 + 120, x2 - 20, y1 + 160)
    screenshot = pyautogui.screenshot(region=textbox_region)
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_BGR2GRAY)

    # 使用Tesseract OCR提取文本
    text = pytesseract.image_to_string(screenshot)
    print("Extracted Text:", text)


def main():
    while True:
        match_template()
        time.sleep(1)  # 每秒检测一次


if __name__ == "__main__":
    main()
