(['D:\\SourceCode\\SteamManifestUpdater\\client_tool.py'],
 ['D:\\SourceCode\\SteamManifestUpdater',
  'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages'],
 [],
 ['D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\numpy\\_pyinstaller',
  'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('client_tool',
   'D:\\SourceCode\\SteamManifestUpdater\\client_tool.py',
   'PYSOURCE')],
 [('pkg_resources',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\struct.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cmd.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.server',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.universal',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cgi.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('keyauth', 'D:\\SourceCode\\SteamManifestUpdater\\keyauth.py', 'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\uuid.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('vdf',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\vdf\\__init__.py',
   'PYMODULE'),
  ('vdf.vdict',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\vdf\\vdict.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Random',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('requests',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\SourceCode\\SteamManifestUpdater\\build\\MTYB_SteamTool\\base_library.zip',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\METADATA',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\REQUESTED',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\top_level.txt',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('wheel-0.38.4.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.38.4.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.38.4.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\METADATA',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\entry_points.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\WHEEL',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\METADATA',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\INSTALLER',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\RECORD',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\top_level.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\LICENSE',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.38.4.dist-info\\LICENSE.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\entry_points.txt',
   'D:\\TARUMT\\FYP\\Coding\\SteamManifestUpdater\\Lib\\site-packages\\wheel-0.38.4.dist-info\\entry_points.txt',
   'DATA')])
